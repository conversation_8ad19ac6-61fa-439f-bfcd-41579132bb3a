const Projects = () => {
  const projects = [
    {
      id: 1,
      name: "E-commerce Database",
      description: "Complete database design for an e-commerce platform",
      status: "Active",
      lastModified: "2 hours ago",
      entities: 12,
      collaborators: 3,
    },
    {
      id: 2,
      name: "User Management System",
      description: "User authentication and authorization system",
      status: "In Review",
      lastModified: "1 day ago",
      entities: 8,
      collaborators: 2,
    },
    {
      id: 3,
      name: "Inventory Tracker",
      description: "Inventory management and tracking system",
      status: "Completed",
      lastModified: "3 days ago",
      entities: 15,
      collaborators: 4,
    },
    {
      id: 4,
      name: "Blog Platform",
      description: "Content management system for blogging",
      status: "Draft",
      lastModified: "1 week ago",
      entities: 6,
      collaborators: 1,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-50 text-green-700 ring-green-600/20";
      case "In Review":
        return "bg-yellow-50 text-yellow-700 ring-yellow-600/20";
      case "Completed":
        return "bg-blue-50 text-blue-700 ring-blue-600/20";
      case "Draft":
        return "bg-gray-50 text-gray-700 ring-gray-600/20";
      default:
        return "bg-gray-50 text-gray-700 ring-gray-600/20";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Projects</h1>
          <p className="text-muted-foreground">
            Manage your database design projects and collaborate with your team.
          </p>
        </div>
        <button className="inline-flex items-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90">
          New Project
        </button>
      </div>

      <div className="grid gap-4">
        {projects.map((project) => (
          <div
            key={project.id}
            className="rounded-lg border bg-card text-card-foreground shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-semibold">{project.name}</h3>
                  <span
                    className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset ${getStatusColor(
                      project.status
                    )}`}
                  >
                    {project.status}
                  </span>
                </div>
                <p className="text-muted-foreground mb-4">{project.description}</p>
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <span>{project.entities} entities</span>
                  <span>{project.collaborators} collaborators</span>
                  <span>Modified {project.lastModified}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button className="inline-flex items-center rounded-md border border-input bg-background px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground">
                  View
                </button>
                <button className="inline-flex items-center rounded-md border border-input bg-background px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground">
                  Edit
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Create Your First Project</h3>
          <p className="text-muted-foreground mb-4">
            Start by creating a new project to organize your ERD designs.
          </p>
          <button className="inline-flex items-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90">
            Get Started
          </button>
        </div>
      </div>
    </div>
  );
};

export default Projects;
